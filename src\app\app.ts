
import { Component, signal } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { HeaderComponent } from './components/header.component';
import { BannerComponent } from './components/banner.component';
import { CategoriesComponent } from './components/categories.component';
import { ProductsComponent } from './components/products.component';
import { FooterComponent } from './components/footer.component';

@Component({
  selector: 'app-root',
  imports: [
    HeaderComponent,
    BannerComponent,
    CategoriesComponent,
    ProductsComponent,
    FooterComponent
  ],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App {
  protected readonly title = signal('aloeepara');
}
