import { Component } from '@angular/core';
import { CurrencyPipe } from '@angular/common';

@Component({
  selector: 'app-products',
  standalone: true,
  imports: [CurrencyPipe],
  templateUrl: './products.component.html',
  styleUrls: ['./products.component.css']
})
export class ProductsComponent {
  products = [
    { name: 'Gel désinfectant', price: 12.5, image: 'assets/images/gel.jpg' },
    { name: 'Crème solaire SPF50', price: 29.9, image: 'assets/images/creme.jpg' },
    { name: 'Complément Vitamine C', price: 19.0, image: 'assets/images/vitc.jpg' },
    { name: 'Shampooing doux', price: 15.5, image: 'assets/images/shampoo.jpg' }
  ];
}
