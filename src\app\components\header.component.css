.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 32px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}
.logo img {
  height: 48px;
}
.menu {
  display: flex;
  gap: 20px;
}
.menu a {
  text-decoration: none;
  color: #222;
  font-weight: 500;
  transition: color 0.2s;
}
.menu a:hover {
  color: #00796b;
}
.search-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}
.search-bar input {
  padding: 6px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.search-bar button {
  background: #00796b;
  color: #fff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
}
.search-bar button:hover {
  background: #004d40;
}
