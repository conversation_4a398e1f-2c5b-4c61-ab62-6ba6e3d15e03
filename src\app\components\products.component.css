.products {
  margin: 32px 0;
}
.products-list {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  justify-content: center;
}
.product-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  width: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  transition: box-shadow 0.2s;
}
.product-card:hover {
  box-shadow: 0 4px 16px rgba(0,121,107,0.15);
}
.product-card img {
  width: 120px;
  height: 120px;
  object-fit: contain;
  margin-bottom: 12px;
}
.product-info {
  text-align: center;
}
.name {
  font-weight: 500;
  margin-bottom: 4px;
}
.price {
  color: #00796b;
  font-weight: bold;
  margin-bottom: 8px;
}
button {
  background: #00796b;
  color: #fff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}
button:hover {
  background: #004d40;
}
